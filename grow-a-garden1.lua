local Fluent = loadstring(game:HttpGet("https://github.com/dawid-scripts/Fluent/releases/latest/download/main.lua"))()
local SaveManager = loadstring(game:HttpGet("https://raw.githubusercontent.com/dawid-scripts/Fluent/master/Addons/SaveManager.lua"))()
local InterfaceManager = loadstring(game:HttpGet("https://raw.githubusercontent.com/dawid-scripts/Fluent/master/Addons/InterfaceManager.lua"))()

-- Game Services
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

local Player = Players.LocalPlayer
local PlayerGui = Player:WaitForChild("PlayerGui")
local GameEvents = ReplicatedStorage:WaitForChild("GameEvents")

-- Global variables for seed functionality
local SeedStock = {}
local SelectedSeeds = {}
local AutoBuyEnabled = false
local AutoBuyConnection = nil
local SeedDropdown = nil

local Window = Fluent:CreateWindow({
    Title = "Fluent " .. Fluent.Version,
    SubTitle = "by XZery",
    TabWidth = 160,
    Size = UDim2.fromOffset(580, 460),
    Acrylic = true, -- The blur may be detectable, setting this to false disables blur entirely
    Theme = "Dark",
    MinimizeKey = Enum.KeyCode.LeftControl -- Used when theres no MinimizeKeybind
})

--Fluent provides Lucide Icons https://lucide.dev/icons/ for the tabs, icons are optional
local Tabs = {
    Main = Window:AddTab({ Title = "Main", Icon = "" }),
    SeedAutoBuy = Window:AddTab({ Title = "Seed Auto-Buy", Icon = "shopping-cart" }),
    Settings = Window:AddTab({ Title = "Settings", Icon = "settings" })
}

local Options = Fluent.Options
    
    local speed = Tabs.Main:AddSlider("Slider", {
        Title = "Speed",
        Description = "Set Your Speed",
        Default = 16,
        Min = 16,
        Max = 200,
        Rounding = 1,
    })

    speed:OnChanged(function(Value)
        local player = game.Players.LocalPlayer
        local char = player.Character
        char.Humanoid.WalkSpeed = Value
    end)

    speed:SetValue(16)

    local jump = Tabs.Main:AddSlider("Slider", {
        Title = "jump",
        Description = "Set Your JumpPower",
        Default = 50,
        Min = 50,
        Max = 200,
        Rounding = 1,
    })

    jump:OnChanged(function(Value)
        local player = game.Players.LocalPlayer
        local char = player.Character
        char.Humanoid.JumpPower = Value
    end)

    jump:SetValue(50)

    -- Fly toggle functionality
    local flyEnabled = false
    local bodyVelocity = nil
    local bodyAngularVelocity = nil
    local connection = nil

    local function enableFly()
        local player = game.Players.LocalPlayer
        local char = player.Character
        if not char or not char:FindFirstChild("HumanoidRootPart") then return end

        local humanoidRootPart = char.HumanoidRootPart

        -- Create BodyVelocity for movement
        bodyVelocity = Instance.new("BodyVelocity")
        bodyVelocity.MaxForce = Vector3.new(9e9, 9e9, 9e9)
        bodyVelocity.Velocity = Vector3.new(0, 0, 0)
        bodyVelocity.Parent = humanoidRootPart

        -- Create BodyAngularVelocity for rotation control
        bodyAngularVelocity = Instance.new("BodyAngularVelocity")
        bodyAngularVelocity.MaxTorque = Vector3.new(0, 9e9, 0)
        bodyAngularVelocity.AngularVelocity = Vector3.new(0, 0, 0)
        bodyAngularVelocity.Parent = humanoidRootPart

        -- Movement control
        connection = game:GetService("RunService").Heartbeat:Connect(function()
            local camera = workspace.CurrentCamera
            local userInputService = game:GetService("UserInputService")

            local velocity = Vector3.new(0, 0, 0)
            local flySpeed = 50

            -- Get camera directions
            local lookDirection = camera.CFrame.LookVector
            local rightDirection = camera.CFrame.RightVector
            local upDirection = Vector3.new(0, 1, 0)

            -- Simple directional movement based on camera
            if userInputService:IsKeyDown(Enum.KeyCode.W) then
                velocity = velocity + lookDirection * flySpeed
            end
            if userInputService:IsKeyDown(Enum.KeyCode.S) then
                velocity = velocity - lookDirection * flySpeed
            end
            if userInputService:IsKeyDown(Enum.KeyCode.A) then
                velocity = velocity - rightDirection * flySpeed
            end
            if userInputService:IsKeyDown(Enum.KeyCode.D) then
                velocity = velocity + rightDirection * flySpeed
            end
            if userInputService:IsKeyDown(Enum.KeyCode.Space) then
                velocity = velocity + upDirection * flySpeed
            end
            if userInputService:IsKeyDown(Enum.KeyCode.LeftShift) then
                velocity = velocity - upDirection * flySpeed
            end

            bodyVelocity.Velocity = velocity
        end)
    end

    local function disableFly()
        if bodyVelocity then
            bodyVelocity:Destroy()
            bodyVelocity = nil
        end
        if bodyAngularVelocity then
            bodyAngularVelocity:Destroy()
            bodyAngularVelocity = nil
        end
        if connection then
            connection:Disconnect()
            connection = nil
        end
    end

    local flyToggle = Tabs.Main:AddToggle("FlyToggle", {
        Title = "Fly",
        Description = "Toggle fly mode",
        Default = false
    })

    flyToggle:OnChanged(function(Value)
        flyEnabled = Value
        if flyEnabled then
            enableFly()
        else
            disableFly()
        end
    end)

    -- Reset Character Button
    local resetButton = Tabs.Main:AddButton({
        Title = "Reset Character",
        Description = "Reset your character",
        Callback = function()
            local player = game.Players.LocalPlayer
            if player.Character then
                player.Character:BreakJoints()
            end
        end
    })

    -- Anti-AFK functionality
    local antiAfkEnabled = false
    local antiAfkConnection = nil
    local lastMoveTime = 0
    local isMoving = false
    local moveStartTime = 0

    local function startAntiAfk()
        antiAfkConnection = game:GetService("RunService").Heartbeat:Connect(function()
            if not antiAfkEnabled then return end

            local currentTime = tick()
            local player = game.Players.LocalPlayer
            local char = player.Character

            if char and char:FindFirstChild("Humanoid") and char:FindFirstChild("HumanoidRootPart") then
                local humanoid = char.Humanoid
                local rootPart = char.HumanoidRootPart

                -- Check if it's time to move (every 30 seconds)
                if not isMoving and (currentTime - lastMoveTime) >= 30 then
                    -- Start moving
                    isMoving = true
                    moveStartTime = currentTime
                    lastMoveTime = currentTime

                    -- Generate random direction
                    local randomX = math.random(-10, 10)
                    local randomZ = math.random(-10, 10)
                    local randomDirection = Vector3.new(randomX, 0, randomZ).Unit

                    -- Move in random direction
                    local targetPosition = rootPart.Position + (randomDirection * 5)
                    humanoid:MoveTo(targetPosition)
                end

                -- Stop moving after 2 seconds
                if isMoving and (currentTime - moveStartTime) >= 2 then
                    isMoving = false
                    humanoid:MoveTo(rootPart.Position) -- Stop at current position
                end
            end
        end)
    end

    local function stopAntiAfk()
        if antiAfkConnection then
            antiAfkConnection:Disconnect()
            antiAfkConnection = nil
        end
    end

    local antiAfkToggle = Tabs.Main:AddToggle("AntiAfkToggle", {
        Title = "Anti-AFK",
        Description = "Auto walk randomly every 30 seconds",
        Default = false
    })

    antiAfkToggle:OnChanged(function(Value)
        antiAfkEnabled = Value
        if antiAfkEnabled then
            startAntiAfk()
        else
            stopAntiAfk()
        end
    end)

-- Seed Auto-Buy Functions
local function GetSeedStock(IgnoreNoStock)
    local SeedShop = PlayerGui.Seed_Shop
    local Items = SeedShop:FindFirstChild("Blueberry", true).Parent

    local NewList = {}

    for _, Item in next, Items:GetChildren() do
        local MainFrame = Item:FindFirstChild("Main_Frame")
        if not MainFrame then continue end

        local StockText = MainFrame.Stock_Text.Text
        local StockCount = tonumber(StockText:match("%d+"))

        --// Separate list
        if IgnoreNoStock then
            if StockCount <= 0 then continue end
            NewList[Item.Name] = StockCount
            continue
        end

        SeedStock[Item.Name] = StockCount
    end

    return IgnoreNoStock and NewList or SeedStock
end

local function BuySeed(Seed)
    GameEvents.BuySeedStock:FireServer(Seed)
end

local function GetAllAvailableSeeds()
    local availableSeeds = {}
    local currentStock = GetSeedStock(true) -- Only get seeds with stock > 0

    for seedName, stock in pairs(currentStock) do
        table.insert(availableSeeds, seedName)
    end

    return availableSeeds
end

local function CheckAndBuySeeds()
    if not AutoBuyEnabled then return end

    local currentStock = GetSeedStock(true) -- Only get seeds with stock > 0

    for seedName, _ in pairs(SelectedSeeds) do
        if currentStock[seedName] and currentStock[seedName] > 0 then
            BuySeed(seedName)
            print("Auto-bought:", seedName, "Stock:", currentStock[seedName])
        end
    end
end

local function UpdateSeedDropdown()
    if not SeedDropdown then return end

    local availableSeeds = GetAllAvailableSeeds()
    SeedDropdown:SetValues(availableSeeds)
end

local function StartAutoBuy()
    if AutoBuyConnection then return end

    AutoBuyConnection = RunService.Heartbeat:Connect(function()
        if AutoBuyEnabled then
            CheckAndBuySeeds()
            -- Update dropdown every few seconds to reflect stock changes
            if tick() % 3 < 0.1 then -- Update every ~3 seconds
                UpdateSeedDropdown()
            end
        end
    end)
end

local function StopAutoBuy()
    if AutoBuyConnection then
        AutoBuyConnection:Disconnect()
        AutoBuyConnection = nil
    end
end

-- Seed Auto-Buy GUI
SeedDropdown = Tabs.SeedAutoBuy:AddDropdown("SeedSelection", {
    Title = "Select Seeds to Auto-Buy",
    Description = "Choose seeds that will be automatically purchased when in stock",
    Values = {},
    Multi = true,
    Default = {}
})

SeedDropdown:OnChanged(function(Values)
    SelectedSeeds = {}
    for _, seedName in pairs(Values) do
        SelectedSeeds[seedName] = true
    end

    -- Save selected seeds
    SaveSelectedSeeds()
end)

local autoBuyToggle = Tabs.SeedAutoBuy:AddToggle("AutoBuyToggle", {
    Title = "Enable Auto-Buy",
    Description = "Automatically buy selected seeds when they are in stock",
    Default = false
})

autoBuyToggle:OnChanged(function(Value)
    AutoBuyEnabled = Value
    if AutoBuyEnabled then
        StartAutoBuy()
        UpdateSeedDropdown() -- Update available seeds when enabling
        Fluent:Notify({
            Title = "Seed Auto-Buy",
            Content = "Auto-buy enabled! Selected seeds will be purchased automatically.",
            Duration = 3
        })
    else
        StopAutoBuy()
        Fluent:Notify({
            Title = "Seed Auto-Buy",
            Content = "Auto-buy disabled.",
            Duration = 3
        })
    end
end)

local refreshButton = Tabs.SeedAutoBuy:AddButton({
    Title = "Refresh Available Seeds",
    Description = "Update the list of available seeds",
    Callback = function()
        UpdateSeedDropdown()
        Fluent:Notify({
            Title = "Seed Auto-Buy",
            Content = "Available seeds list updated!",
            Duration = 2
        })
    end
})

local stockInfoButton = Tabs.SeedAutoBuy:AddButton({
    Title = "Show Current Stock",
    Description = "Display current seed stock information",
    Callback = function()
        local currentStock = GetSeedStock(false) -- Get all seeds including zero stock
        local stockInfo = "Current Seed Stock:\n"

        for seedName, stock in pairs(currentStock) do
            stockInfo = stockInfo .. seedName .. ": " .. stock .. "\n"
        end

        print(stockInfo)
        Fluent:Notify({
            Title = "Stock Info",
            Content = "Stock information printed to console",
            Duration = 3
        })
    end
})

-- Initialize dropdown with available seeds
spawn(function()
    wait(2) -- Wait for game to load
    UpdateSeedDropdown()
end)

-- Addons:
-- SaveManager (Allows you to have a configuration system)
-- InterfaceManager (Allows you to have a interface managment system)

-- Hand the library over to our managers
SaveManager:SetLibrary(Fluent)
InterfaceManager:SetLibrary(Fluent)

-- Ignore keys that are used by ThemeManager.
-- (we dont want configs to save themes, do we?)
SaveManager:IgnoreThemeSettings()

-- You can add indexes of elements the save manager should ignore
SaveManager:SetIgnoreIndexes({})

-- Save selected seeds configuration
SaveManager:SetIgnoreIndexes({})

-- Load saved selected seeds
local function LoadSelectedSeeds()
    local savedSeeds = SaveManager:Get("SelectedSeeds")
    if savedSeeds then
        SelectedSeeds = savedSeeds
        -- Update dropdown selection
        local seedList = {}
        for seedName, _ in pairs(SelectedSeeds) do
            table.insert(seedList, seedName)
        end
        if SeedDropdown then
            SeedDropdown:SetValue(seedList)
        end
    end
end

-- Save selected seeds
local function SaveSelectedSeeds()
    SaveManager:Set("SelectedSeeds", SelectedSeeds)
    SaveManager:Save()
end

-- use case for doing it this way:
-- a script hub could have themes in a global folder
-- and game configs in a separate folder per game
InterfaceManager:SetFolder("FluentScriptHub")
SaveManager:SetFolder("FluentScriptHub/specific-game")

InterfaceManager:BuildInterfaceSection(Tabs.Settings)
SaveManager:BuildConfigSection(Tabs.Settings)


Window:SelectTab(1)

Fluent:Notify({
    Title = "Fluent",
    Content = "The script has been loaded.",
    Duration = 8
})

-- You can use the SaveManager:LoadAutoloadConfig() to load a config
-- which has been marked to be one that auto loads!
SaveManager:LoadAutoloadConfig()