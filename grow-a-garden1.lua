local Fluent = loadstring(game:HttpGet("https://github.com/dawid-scripts/Fluent/releases/latest/download/main.lua"))()
local SaveManager = loadstring(game:HttpGet("https://raw.githubusercontent.com/dawid-scripts/Fluent/master/Addons/SaveManager.lua"))()
local InterfaceManager = loadstring(game:HttpGet("https://raw.githubusercontent.com/dawid-scripts/Fluent/master/Addons/InterfaceManager.lua"))()

local Window = Fluent:CreateWindow({
    Title = "Fluent " .. Fluent.Version,
    SubTitle = "by XZery",
    TabWidth = 160,
    Size = UDim2.fromOffset(580, 460),
    Acrylic = true, -- The blur may be detectable, setting this to false disables blur entirely
    Theme = "Dark",
    MinimizeKey = Enum.KeyCode.LeftControl -- Used when theres no MinimizeKeybind
})

--Fluent provides Lucide Icons https://lucide.dev/icons/ for the tabs, icons are optional
local Tabs = {
    Main = Window:AddTab({ Title = "Main", Icon = "" }),
    Settings = Window:AddTab({ Title = "Settings", Icon = "settings" })
}

local Options = Fluent.Options
    
    local speed = Tabs.Main:AddSlider("Slider", {
        Title = "Speed",
        Description = "Set Your Speed",
        Default = 16,
        Min = 16,
        Max = 200,
        Rounding = 1,
    })

    speed:OnChanged(function(Value)
        local player = game.Players.LocalPlayer
        local char = player.Character
        char.Humanoid.WalkSpeed = Value
    end)

    speed:SetValue(16)

    local jump = Tabs.Main:AddSlider("Slider", {
        Title = "jump",
        Description = "Set Your JumpPower",
        Default = 50,
        Min = 50,
        Max = 200,
        Rounding = 1,
    })

    jump:OnChanged(function(Value)
        local player = game.Players.LocalPlayer
        local char = player.Character
        char.Humanoid.JumpPower = Value
    end)

    jump:SetValue(50)

    -- Fly toggle functionality
    local flyEnabled = false
    local bodyVelocity = nil
    local bodyAngularVelocity = nil
    local connection = nil

    local function enableFly()
        local player = game.Players.LocalPlayer
        local char = player.Character
        if not char or not char:FindFirstChild("HumanoidRootPart") then return end

        local humanoidRootPart = char.HumanoidRootPart

        -- Create BodyVelocity for movement
        bodyVelocity = Instance.new("BodyVelocity")
        bodyVelocity.MaxForce = Vector3.new(9e9, 9e9, 9e9)
        bodyVelocity.Velocity = Vector3.new(0, 0, 0)
        bodyVelocity.Parent = humanoidRootPart

        -- Create BodyAngularVelocity for rotation control
        bodyAngularVelocity = Instance.new("BodyAngularVelocity")
        bodyAngularVelocity.MaxTorque = Vector3.new(0, 9e9, 0)
        bodyAngularVelocity.AngularVelocity = Vector3.new(0, 0, 0)
        bodyAngularVelocity.Parent = humanoidRootPart

        -- Movement control
        connection = game:GetService("RunService").Heartbeat:Connect(function()
            local camera = workspace.CurrentCamera
            local userInputService = game:GetService("UserInputService")

            local velocity = Vector3.new(0, 0, 0)
            local flySpeed = 50

            -- Get camera directions
            local lookDirection = camera.CFrame.LookVector
            local rightDirection = camera.CFrame.RightVector
            local upDirection = Vector3.new(0, 1, 0)

            -- Simple directional movement based on camera
            if userInputService:IsKeyDown(Enum.KeyCode.W) then
                velocity = velocity + lookDirection * flySpeed
            end
            if userInputService:IsKeyDown(Enum.KeyCode.S) then
                velocity = velocity - lookDirection * flySpeed
            end
            if userInputService:IsKeyDown(Enum.KeyCode.A) then
                velocity = velocity - rightDirection * flySpeed
            end
            if userInputService:IsKeyDown(Enum.KeyCode.D) then
                velocity = velocity + rightDirection * flySpeed
            end
            if userInputService:IsKeyDown(Enum.KeyCode.Space) then
                velocity = velocity + upDirection * flySpeed
            end
            if userInputService:IsKeyDown(Enum.KeyCode.LeftShift) then
                velocity = velocity - upDirection * flySpeed
            end

            bodyVelocity.Velocity = velocity
        end)
    end

    local function disableFly()
        if bodyVelocity then
            bodyVelocity:Destroy()
            bodyVelocity = nil
        end
        if bodyAngularVelocity then
            bodyAngularVelocity:Destroy()
            bodyAngularVelocity = nil
        end
        if connection then
            connection:Disconnect()
            connection = nil
        end
    end

    local flyToggle = Tabs.Main:AddToggle("FlyToggle", {
        Title = "Fly",
        Description = "Toggle fly mode",
        Default = false
    })

    flyToggle:OnChanged(function(Value)
        flyEnabled = Value
        if flyEnabled then
            enableFly()
        else
            disableFly()
        end
    end)

    -- Reset Character Button
    local resetButton = Tabs.Main:AddButton({
        Title = "Reset Character",
        Description = "Reset your character",
        Callback = function()
            local player = game.Players.LocalPlayer
            if player.Character then
                player.Character:BreakJoints()
            end
        end
    })

    -- Anti-AFK functionality
    local antiAfkEnabled = false
    local antiAfkConnection = nil
    local lastMoveTime = 0
    local isMoving = false
    local moveStartTime = 0

    local function startAntiAfk()
        antiAfkConnection = game:GetService("RunService").Heartbeat:Connect(function()
            if not antiAfkEnabled then return end

            local currentTime = tick()
            local player = game.Players.LocalPlayer
            local char = player.Character

            if char and char:FindFirstChild("Humanoid") and char:FindFirstChild("HumanoidRootPart") then
                local humanoid = char.Humanoid
                local rootPart = char.HumanoidRootPart

                -- Check if it's time to move (every 30 seconds)
                if not isMoving and (currentTime - lastMoveTime) >= 30 then
                    -- Start moving
                    isMoving = true
                    moveStartTime = currentTime
                    lastMoveTime = currentTime

                    -- Generate random direction
                    local randomX = math.random(-10, 10)
                    local randomZ = math.random(-10, 10)
                    local randomDirection = Vector3.new(randomX, 0, randomZ).Unit

                    -- Move in random direction
                    local targetPosition = rootPart.Position + (randomDirection * 5)
                    humanoid:MoveTo(targetPosition)
                end

                -- Stop moving after 2 seconds
                if isMoving and (currentTime - moveStartTime) >= 2 then
                    isMoving = false
                    humanoid:MoveTo(rootPart.Position) -- Stop at current position
                end
            end
        end)
    end

    local function stopAntiAfk()
        if antiAfkConnection then
            antiAfkConnection:Disconnect()
            antiAfkConnection = nil
        end
    end

    local antiAfkToggle = Tabs.Main:AddToggle("AntiAfkToggle", {
        Title = "Anti-AFK",
        Description = "Auto walk randomly every 30 seconds",
        Default = false
    })

    antiAfkToggle:OnChanged(function(Value)
        antiAfkEnabled = Value
        if antiAfkEnabled then
            startAntiAfk()
        else
            stopAntiAfk()
        end
    end)

-- Addons:
-- SaveManager (Allows you to have a configuration system)
-- InterfaceManager (Allows you to have a interface managment system)

-- Hand the library over to our managers
SaveManager:SetLibrary(Fluent)
InterfaceManager:SetLibrary(Fluent)

-- Ignore keys that are used by ThemeManager.
-- (we dont want configs to save themes, do we?)
SaveManager:IgnoreThemeSettings()

-- You can add indexes of elements the save manager should ignore
SaveManager:SetIgnoreIndexes({})

-- use case for doing it this way:
-- a script hub could have themes in a global folder
-- and game configs in a separate folder per game
InterfaceManager:SetFolder("FluentScriptHub")
SaveManager:SetFolder("FluentScriptHub/specific-game")

InterfaceManager:BuildInterfaceSection(Tabs.Settings)
SaveManager:BuildConfigSection(Tabs.Settings)


Window:SelectTab(1)

Fluent:Notify({
    Title = "Fluent",
    Content = "The script has been loaded.",
    Duration = 8
})

-- You can use the SaveManager:LoadAutoloadConfig() to load a config
-- which has been marked to be one that auto loads!
SaveManager:LoadAutoloadConfig()